# AWS Textract OCR Integration & PDF Highlighting Enhancement

## Overview

This document outlines the implementation plan for enhancing the knowledge-sphere platform's text extraction capabilities by integrating AWS Textract OCR services and adding interactive highlighting to the PDF viewer.

## Current State Analysis

### Current Text Extraction System
- Uses `pdf-parse` library for basic text extraction from PDFs
- Text processing in `/src/lib/materials/processor.ts:54` using PDFLoader from LangChain
- Simple text chunking with RecursiveCharacterTextSplitter (1000 char chunks, 200 overlap)
- No geometry/layout information preserved

### Current PDF Viewer
- Built with `@react-pdf-viewer` components in `/src/components/documents/pdf-viewer.tsx`
- Basic viewing functionality with page navigation
- No highlighting or annotation capabilities
- Uses PDF.js under the hood

## Enhancement Goals

1. **Improve Text Extraction Reliability**: Replace basic pdf-parse with AWS Textract's advanced OCR
2. **Add Layout Understanding**: Utilize Textract's LAYOUT feature to identify document structure
3. **Visual Feedback**: Highlight extracted regions in PDF viewer with confidence indicators
4. **Enhanced User Experience**: Allow users to interact with highlighted regions for AI referencing

## Implementation Plan

### Phase 1: AWS Textract Integration (Backend)

#### 1. AWS Textract Service Setup
- Add AWS Textract client to existing AWS services
- Create Textract service wrapper in `/src/lib/aws/textract.ts`
- Configure environment variables for AWS Textract access
- Add `@aws-sdk/client-textract` dependency

#### 2. Enhanced Text Extraction Pipeline
- Modify `/src/lib/materials/processor.ts` to support dual extraction modes:
  - Fallback: Current pdf-parse method
  - Enhanced: AWS Textract AnalyzeDocument with LAYOUT feature
- Store geometry data (bounding boxes, polygons) alongside text chunks
- Integrate Textract processing into existing upload workflow (internal service)

#### 3. Upload Process Enhancement
- Extend `/src/app/api/materials/upload/route.ts` to include Textract processing
- Process OCR during upload pipeline automatically for PDF files
- No additional API endpoints needed - all handled internally

### Phase 2: Database Schema Updates

#### 1. OCR Metadata Storage
- Add `ocr_analysis` table to store Textract results
- Add geometry columns to existing `chunks` table:
  - `bounding_box` (JSON): {height, left, top, width}
  - `polygon_coordinates` (JSON): Array of {x, y} points
  - `layout_type` (enum): PARAGRAPH, TITLE, HEADER, etc.
  - `confidence_score` (float): Textract confidence level

#### 2. Document Processing Status
- Add `status` column to documents table (uploaded, processing, completed, failed)
- Generic status to support future Upstash workflow separation:
  - uploading → ocr_extracting → embedding → completed
- Track processing metadata and errors

### Phase 3: PDF Viewer Enhancement (Frontend)

#### 1. Highlighting System
- Create `PDFHighlightLayer` component using SVG overlays
- Implement coordinate transformation (Textract normalized → PDF viewer pixels)
- Add highlighting styles for different layout types (paragraphs, titles, headers)
- Render clickable highlight regions over PDF pages

#### 2. Enhanced PDF Viewer Component
- Extend `/src/components/documents/pdf-viewer.tsx`:
  - Add highlight layer management
  - Implement coordinate scaling based on zoom level
  - Handle page changes and highlight persistence

#### 3. Interactive Features
- Hover effects showing extraction confidence and layout type
- Click to select highlighted blocks for AI reference
- Tooltip showing extracted text content
- Visual feedback for successfully extracted vs. problematic areas

### Phase 4: User Experience Features

#### 1. Extraction Quality Indicators
- Color-coded highlights based on confidence scores:
  - Green: High confidence (>90%)
  - Yellow: Medium confidence (70-90%)
  - Red: Low confidence (<70%)
- Error indicators for failed extractions

#### 2. AI Integration Enhancement
- Enable users to click highlighted blocks to reference specific text
- Auto-populate chat context with selected PDF regions
- Show "source highlighting" when AI references specific document sections

#### 3. Extraction Management UI
- Processing status indicators during document processing
- Re-processing options for failed documents
- Extraction quality reports and statistics

### Phase 5: Performance & Optimization

#### 1. Caching Strategy
- Cache Textract results to avoid re-processing
- Implement efficient geometry data loading
- Lazy loading of highlight overlays

#### 2. Error Handling & Fallbacks
- Graceful degradation when Textract is unavailable
- Fallback to current pdf-parse extraction
- User feedback for processing failures

## Implementation Order

1. **Backend Foundation**: AWS Textract service, database schema updates
2. **Upload Integration**: Textract processing in existing upload workflow
3. **Frontend Highlighting**: Basic PDF overlay system with geometry rendering
4. **User Interactions**: Hover, click, and selection functionality
5. **AI Integration**: Enhanced chat context with highlighted regions
6. **Polish & Performance**: Optimization, error handling, UX improvements

## Technical Specifications

### AWS Textract AnalyzeDocument API
- **Features Used**: TEXT, LAYOUT
- **Response Structure**: Blocks with geometry information
- **Coordinate System**: Normalized (0-1 scale) bounding boxes and polygons
- **Layout Elements**: PARAGRAPH, TITLE, HEADER, FOOTER, SECTION_HEADER

### Geometry Data Structure
```json
{
  "BoundingBox": {
    "Width": 0.8,
    "Height": 0.05,
    "Left": 0.1,
    "Top": 0.3
  },
  "Polygon": [
    {"X": 0.1, "Y": 0.3},
    {"X": 0.9, "Y": 0.3},
    {"X": 0.9, "Y": 0.35},
    {"X": 0.1, "Y": 0.35}
  ]
}
```

### Database Schema Changes
```sql
-- Add status column to documents
ALTER TABLE documents ADD COLUMN status VARCHAR(20) DEFAULT 'uploaded';

-- Create OCR analysis table
CREATE TABLE ocr_analysis (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  document_id UUID REFERENCES documents(id) ON DELETE CASCADE,
  textract_job_id VARCHAR(255),
  analysis_data JSONB,
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

-- Add geometry columns to chunks
ALTER TABLE chunks ADD COLUMN bounding_box JSONB;
ALTER TABLE chunks ADD COLUMN polygon_coordinates JSONB;
ALTER TABLE chunks ADD COLUMN layout_type VARCHAR(50);
ALTER TABLE chunks ADD COLUMN confidence_score REAL;
```

## Technical Considerations

- **Cost Management**: Textract pricing per page (~$0.0015/page)
- **Processing Time**: Async processing for large documents within upload flow
- **Coordinate Precision**: Textract uses normalized coordinates (0-1 scale)
- **PDF Scaling**: Handle different zoom levels and responsive layouts
- **Browser Compatibility**: SVG overlay support across browsers
- **Future Workflow**: Generic `status` column supports Upstash workflow separation

## Success Metrics

1. **Extraction Accuracy**: Improved text extraction quality vs. pdf-parse baseline
2. **User Engagement**: Interaction rates with highlighted PDF regions
3. **Processing Efficiency**: Document processing time including OCR
4. **Error Rates**: Failed extraction percentages and recovery success
5. **Cost Efficiency**: Textract usage costs vs. extraction value

## Risk Mitigation

1. **Service Availability**: Fallback to pdf-parse when Textract unavailable
2. **Cost Control**: Monitoring and alerting for Textract usage spikes
3. **Performance**: Lazy loading and caching to prevent UI slowdowns
4. **Data Privacy**: Ensure compliance with document processing policies

This enhancement will significantly improve text extraction reliability while providing users with visual feedback on extraction quality and enabling more precise document referencing in AI conversations.