import { type OCRHighlight } from '@/components/documents/enhanced-pdf-viewer';
import { type DocumentChunk, type OcrAnalysis } from '@/database/schema';
import { type TextractResult } from '@/lib/aws/textract';

/**
 * Convert document chunks with OCR data to PDF highlights
 */
export function convertChunksToHighlights(chunks: DocumentChunk[]): OCRHighlight[] {
  const highlights: OCRHighlight[] = [];

  chunks.forEach((chunk) => {
    if (!chunk.boundingBox || !chunk.layoutType) {
      return; // Skip chunks without OCR data
    }

    try {
      // Parse bounding boxes (could be an array for chunks spanning multiple regions)
      const boundingBoxes = Array.isArray(chunk.boundingBox) 
        ? chunk.boundingBox 
        : [chunk.boundingBox];

      // Parse polygon coordinates if available
      const polygons = chunk.polygonCoordinates 
        ? (Array.isArray(chunk.polygonCoordinates) ? chunk.polygonCoordinates : [chunk.polygonCoordinates])
        : [];

      boundingBoxes.forEach((bbox, index) => {
        // Extract page index from metadata or use 0 as default
        const pageIndex = (chunk.metadata as any)?.pageIndex || 0;

        const highlight: OCRHighlight = {
          id: `${chunk.id}-${index}`,
          pageIndex,
          boundingBox: {
            left: bbox.left || 0,
            top: bbox.top || 0,
            width: bbox.width || 0,
            height: bbox.height || 0,
          },
          polygon: polygons[index] || undefined,
          text: chunk.content,
          layoutType: chunk.layoutType || 'UNKNOWN',
          confidence: chunk.confidenceScore ? chunk.confidenceScore / 100 : 0,
          chunkId: chunk.id?.toString() || '',
        };

        highlights.push(highlight);
      });
    } catch (error) {
      console.warn('Failed to parse OCR data for chunk:', chunk.id, error);
    }
  });

  return highlights;
}

/**
 * Convert Textract analysis data to PDF highlights
 */
export function convertTextractToHighlights(ocrAnalysis: OcrAnalysis): OCRHighlight[] {
  const highlights: OCRHighlight[] = [];

  try {
    const analysisData = ocrAnalysis.analysisData as TextractResult;
    
    analysisData.extractedText.forEach((item, index) => {
      const highlight: OCRHighlight = {
        id: `textract-${ocrAnalysis.id}-${index}`,
        pageIndex: item.pageIndex || 0, // Use pageIndex from Textract data
        boundingBox: {
          left: item.geometry.boundingBox.left,
          top: item.geometry.boundingBox.top,
          width: item.geometry.boundingBox.width,
          height: item.geometry.boundingBox.height,
        },
        polygon: item.geometry.polygon,
        text: item.text,
        layoutType: item.layoutType,
        confidence: item.confidence,
      };

      highlights.push(highlight);
    });
  } catch (error) {
    console.warn('Failed to parse Textract analysis data:', error);
  }

  return highlights;
}

/**
 * Group highlights by confidence levels
 */
export function groupHighlightsByConfidence(highlights: OCRHighlight[]) {
  return {
    high: highlights.filter(h => h.confidence >= 0.9),
    medium: highlights.filter(h => h.confidence >= 0.7 && h.confidence < 0.9),
    low: highlights.filter(h => h.confidence < 0.7),
  };
}

/**
 * Group highlights by layout type
 */
export function groupHighlightsByLayoutType(highlights: OCRHighlight[]) {
  const grouped: Record<string, OCRHighlight[]> = {};
  
  highlights.forEach(highlight => {
    if (!grouped[highlight.layoutType]) {
      grouped[highlight.layoutType] = [];
    }
    grouped[highlight.layoutType].push(highlight);
  });

  return grouped;
}

/**
 * Filter highlights by confidence threshold
 */
export function filterHighlightsByConfidence(
  highlights: OCRHighlight[], 
  minConfidence: number
): OCRHighlight[] {
  return highlights.filter(h => h.confidence >= minConfidence);
}

/**
 * Get highlight statistics
 */
export function getHighlightStatistics(highlights: OCRHighlight[]) {
  const total = highlights.length;
  const confidenceGroups = groupHighlightsByConfidence(highlights);
  const layoutGroups = groupHighlightsByLayoutType(highlights);

  const avgConfidence = highlights.length > 0 
    ? highlights.reduce((sum, h) => sum + h.confidence, 0) / highlights.length
    : 0;

  const uniqueLayoutTypes = Object.keys(layoutGroups);

  return {
    total,
    averageConfidence: avgConfidence,
    confidenceDistribution: {
      high: confidenceGroups.high.length,
      medium: confidenceGroups.medium.length,
      low: confidenceGroups.low.length,
    },
    layoutTypes: uniqueLayoutTypes,
    layoutTypeDistribution: Object.fromEntries(
      uniqueLayoutTypes.map(type => [type, layoutGroups[type].length])
    ),
  };
}