import { env } from '@/lib/env.mjs';
import {
  TextractClient,
  AnalyzeDocumentCommand,
  FeatureType,
  Block,
  BoundingBox,
  Point,
} from '@aws-sdk/client-textract';

export const textractClient = new TextractClient({
  region: env.AWS_REGION || 'us-east-1',
  credentials: {
    accessKeyId: env.AWS_ACCESS_KEY_ID,
    secretAccessKey: env.AWS_SECRET_ACCESS_KEY,
  },
});

export interface TextractGeometry {
  boundingBox: {
    width: number;
    height: number;
    left: number;
    top: number;
  };
  polygon: Array<{ x: number; y: number }>;
}

export interface TextractExtractedText {
  text: string;
  confidence: number;
  geometry: TextractGeometry;
  layoutType: string;
  blockId: string;
  pageIndex: number;
}

export interface TextractResult {
  extractedText: TextractExtractedText[];
  layoutElements: {
    paragraphs: TextractExtractedText[];
    titles: TextractExtractedText[];
    headers: TextractExtractedText[];
    footers: TextractExtractedText[];
    sectionHeaders: TextractExtractedText[];
  };
  rawBlocks: Block[];
}

/**
 * Convert Textract geometry to our standard format
 */
function convertGeometry(geometry: any): TextractGeometry {
  const boundingBox = geometry?.BoundingBox || {};
  const polygon = geometry?.Polygon || [];

  return {
    boundingBox: {
      width: boundingBox.Width || 0,
      height: boundingBox.Height || 0,
      left: boundingBox.Left || 0,
      top: boundingBox.Top || 0,
    },
    polygon: polygon.map((point: Point) => ({
      x: point.X || 0,
      y: point.Y || 0,
    })),
  };
}

/**
 * Extract text from a PDF document using AWS Textract with LAYOUT analysis
 * @param documentBytes Buffer containing the PDF document
 * @param debug Whether to log detailed processing information
 * @returns Extracted text with layout and geometry information
 */
export async function analyzeDocumentWithTextract(
  documentBytes: Buffer,
  debug = false
): Promise<TextractResult> {
  if (debug) {
    console.log('🔍 Starting Textract analysis...');
    console.log(`Document size: ${(documentBytes.length / 1024 / 1024).toFixed(2)} MB`);
  }

  const startTime = performance.now();

  try {
    const command = new AnalyzeDocumentCommand({
      Document: {
        Bytes: documentBytes,
      },
      FeatureTypes: [FeatureType.LAYOUT],
    });

    const response = await textractClient.send(command);
    const endTime = performance.now();

    if (debug) {
      console.log(`✅ Textract analysis completed in ${((endTime - startTime) / 1000).toFixed(2)}s`);
      console.log(`Found ${response.Blocks?.length || 0} blocks`);
    }

    if (!response.Blocks) {
      throw new Error('No blocks returned from Textract');
    }

    const extractedText: TextractExtractedText[] = [];
    const layoutElements = {
      paragraphs: [] as TextractExtractedText[],
      titles: [] as TextractExtractedText[],
      headers: [] as TextractExtractedText[],
      footers: [] as TextractExtractedText[],
      sectionHeaders: [] as TextractExtractedText[],
    };

    // Process blocks and extract text with layout information
    for (const block of response.Blocks) {
      if (!block.Text || !block.Confidence || !block.Geometry) {
        continue;
      }

      // Extract page index from block metadata (Textract provides page info)
      const pageIndex = (block.Page || 1) - 1; // Convert to 0-based indexing

      const extractedItem: TextractExtractedText = {
        text: block.Text,
        confidence: block.Confidence / 100, // Convert to 0-1 scale
        geometry: convertGeometry(block.Geometry),
        layoutType: block.BlockType || 'UNKNOWN',
        blockId: block.Id || '',
        pageIndex,
      };

      extractedText.push(extractedItem);

      // Categorize by layout type
      switch (block.BlockType) {
        case 'LAYOUT_TEXT':
          // For layout text, check if it's part of a specific layout element
          if (block.Text.length > 50) {
            layoutElements.paragraphs.push(extractedItem);
          }
          break;
        case 'LAYOUT_TITLE':
          layoutElements.titles.push(extractedItem);
          break;
        case 'LAYOUT_HEADER':
          layoutElements.headers.push(extractedItem);
          break;
        case 'LAYOUT_FOOTER':
          layoutElements.footers.push(extractedItem);
          break;
        case 'LAYOUT_SECTION_HEADER':
          layoutElements.sectionHeaders.push(extractedItem);
          break;
        case 'LINE':
          // Lines are individual text lines, group them as paragraphs if they're substantial
          if (block.Text.length > 20) {
            layoutElements.paragraphs.push(extractedItem);
          }
          break;
      }
    }

    if (debug) {
      console.log('📊 Textract extraction summary:');
      console.log(`  - Total text blocks: ${extractedText.length}`);
      console.log(`  - Paragraphs: ${layoutElements.paragraphs.length}`);
      console.log(`  - Titles: ${layoutElements.titles.length}`);
      console.log(`  - Headers: ${layoutElements.headers.length}`);
      console.log(`  - Footers: ${layoutElements.footers.length}`);
      console.log(`  - Section headers: ${layoutElements.sectionHeaders.length}`);

      // Show confidence distribution
      const confidences = extractedText.map((item) => item.confidence);
      const avgConfidence = confidences.reduce((a, b) => a + b, 0) / confidences.length;
      const minConfidence = Math.min(...confidences);
      const maxConfidence = Math.max(...confidences);

      console.log(`  - Confidence: avg=${(avgConfidence * 100).toFixed(1)}%, min=${(minConfidence * 100).toFixed(1)}%, max=${(maxConfidence * 100).toFixed(1)}%`);
    }

    return {
      extractedText,
      layoutElements,
      rawBlocks: response.Blocks,
    };
  } catch (error) {
    console.error('❌ Textract analysis failed:', error);
    throw new Error(`Textract analysis failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

/**
 * Check if AWS Textract service is available and properly configured
 */
export async function checkTextractAvailability(): Promise<boolean> {
  try {
    // Simple test to verify credentials and service availability
    const testCommand = new AnalyzeDocumentCommand({
      Document: {
        Bytes: Buffer.from('test'),
      },
      FeatureTypes: [FeatureType.LAYOUT],
    });

    // This will fail with invalid document but should not fail with auth errors
    await textractClient.send(testCommand);
    return true;
  } catch (error: any) {
    // If it's an invalid document error, credentials are working
    if (error.name === 'InvalidParameterException' || error.name === 'UnsupportedDocumentException') {
      return true;
    }
    
    // Other errors indicate service unavailability or auth issues
    console.warn('Textract service unavailable:', error.message);
    return false;
  }
}

/**
 * Process text blocks into chunks suitable for embedding
 * This combines related text blocks and maintains geometry information
 */
export function processTextractBlocksToChunks(
  result: TextractResult,
  chunkSize = 1000,
  chunkOverlap = 200
): Array<{
  content: string;
  chunkOrder: number;
  geometry: TextractGeometry[];
  layoutTypes: string[];
  confidenceScore: number;
}> {
  const chunks = [];
  let currentChunk = '';
  let currentGeometry: TextractGeometry[] = [];
  let currentLayoutTypes: string[] = [];
  let currentConfidences: number[] = [];
  let chunkOrder = 0;

  // Sort text blocks by position (top to bottom, left to right)
  const sortedText = result.extractedText.sort((a, b) => {
    const topDiff = a.geometry.boundingBox.top - b.geometry.boundingBox.top;
    if (Math.abs(topDiff) < 0.01) {
      // If roughly same vertical position, sort by horizontal position
      return a.geometry.boundingBox.left - b.geometry.boundingBox.left;
    }
    return topDiff;
  });

  for (const textItem of sortedText) {
    const newText = textItem.text + ' ';
    
    // Check if adding this text would exceed chunk size
    if (currentChunk.length + newText.length > chunkSize && currentChunk.length > 0) {
      // Save current chunk
      chunks.push({
        content: currentChunk.trim(),
        chunkOrder,
        geometry: [...currentGeometry],
        layoutTypes: Array.from(new Set(currentLayoutTypes)),
        confidenceScore: currentConfidences.reduce((a, b) => a + b, 0) / currentConfidences.length,
      });

      // Start new chunk with overlap
      const overlapText = currentChunk.slice(-chunkOverlap);
      currentChunk = overlapText + newText;
      currentGeometry = [textItem.geometry];
      currentLayoutTypes = [textItem.layoutType];
      currentConfidences = [textItem.confidence];
      chunkOrder++;
    } else {
      // Add to current chunk
      currentChunk += newText;
      currentGeometry.push(textItem.geometry);
      currentLayoutTypes.push(textItem.layoutType);
      currentConfidences.push(textItem.confidence);
    }
  }

  // Don't forget the last chunk
  if (currentChunk.trim().length > 0) {
    chunks.push({
      content: currentChunk.trim(),
      chunkOrder,
      geometry: currentGeometry,
      layoutTypes: Array.from(new Set(currentLayoutTypes)),
      confidenceScore: currentConfidences.reduce((a, b) => a + b, 0) / currentConfidences.length,
    });
  }

  return chunks;
}