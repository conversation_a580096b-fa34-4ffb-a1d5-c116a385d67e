import { db } from '@/database/drizzle';
import { documentChunks, documents, ocrAnalysis } from '@/database/schema';
import { MaterialRepository } from '@/database/repository/material';
import { getUser } from '@/lib/auth';
import { convertChunksToHighlights, convertTextractToHighlights } from '@/lib/materials/ocr-utils';
import { and, eq, isNotNull } from 'drizzle-orm';
import { NextRequest, NextResponse } from 'next/server';

export async function GET(
  request: NextRequest,
  { params }: { params: { documentId: string } }
) {
  try {
    const { user } = await getUser();
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { documentId } = params;

    // Verify user has access to this document
    const document = await db
      .select()
      .from(documents)
      .where(eq(documents.id, documentId))
      .limit(1);

    if (!document[0]) {
      return NextResponse.json({ error: 'Document not found' }, { status: 404 });
    }

    // Check if user has access to this document (owner or shared space)
    if (document[0].uploadedBy !== user.id) {
      // TODO: Add space permission check here
      // For now, we'll just allow access
    }

    const searchParams = request.nextUrl.searchParams;
    const source = searchParams.get('source') || 'chunks'; // 'chunks' or 'textract'
    const minConfidence = parseFloat(searchParams.get('minConfidence') || '0');

    if (source === 'textract') {
      // Get highlights from Textract analysis
      const ocrData = await MaterialRepository.getOcrAnalysis(documentId);
      
      if (!ocrData) {
        return NextResponse.json({ 
          highlights: [], 
          message: 'No Textract analysis found for this document' 
        });
      }

      const highlights = convertTextractToHighlights(ocrData);
      const filteredHighlights = highlights.filter(h => h.confidence >= minConfidence);

      return NextResponse.json({
        highlights: filteredHighlights,
        source: 'textract',
        totalBlocks: ocrData.totalBlocks,
        averageConfidence: ocrData.averageConfidence,
        processingTime: ocrData.processingTime,
      });
    } else {
      // Get highlights from document chunks
      const chunks = await db
        .select()
        .from(documentChunks)
        .where(
          and(
            eq(documentChunks.documentId, documentId),
            isNotNull(documentChunks.boundingBox),
            isNotNull(documentChunks.layoutType)
          )
        )
        .orderBy(documentChunks.chunkOrder);

      if (chunks.length === 0) {
        return NextResponse.json({ 
          highlights: [], 
          message: 'No OCR data found for this document' 
        });
      }

      const highlights = convertChunksToHighlights(chunks);
      const filteredHighlights = highlights.filter(h => h.confidence >= minConfidence);

      return NextResponse.json({
        highlights: filteredHighlights,
        source: 'chunks',
        totalChunks: chunks.length,
        chunksWithOcr: chunks.filter(c => c.boundingBox && c.layoutType).length,
      });
    }
  } catch (error) {
    console.error('Error fetching document highlights:', error);
    return NextResponse.json(
      { error: 'Failed to fetch document highlights' },
      { status: 500 }
    );
  }
}