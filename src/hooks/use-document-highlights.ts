import { type OCRHighlight } from '@/components/documents/enhanced-pdf-viewer';
import { useQuery } from '@tanstack/react-query';
import { useState, useCallback } from 'react';

interface UseDocumentHighlightsOptions {
  source?: 'chunks' | 'textract';
  minConfidence?: number;
  enabled?: boolean;
}

interface HighlightsResponse {
  highlights: OCRHighlight[];
  source: string;
  message?: string;
  totalBlocks?: number;
  totalChunks?: number;
  chunksWithOcr?: number;
  averageConfidence?: number;
  processingTime?: number;
}

export function useDocumentHighlights(
  documentId: string,
  options: UseDocumentHighlightsOptions = {}
) {
  const {
    source = 'chunks',
    minConfidence = 0,
    enabled = true,
  } = options;

  const [selectedHighlight, setSelectedHighlight] = useState<OCRHighlight | null>(null);
  const [hoveredHighlight, setHoveredHighlight] = useState<OCRHighlight | null>(null);
  const [confidenceFilter, setConfidenceFilter] = useState(minConfidence);
  const [layoutTypeFilter, setLayoutTypeFilter] = useState<string | null>(null);

  // Fetch highlights data
  const {
    data: highlightsData,
    isLoading,
    error,
    refetch,
  } = useQuery<HighlightsResponse>({
    queryKey: ['document-highlights', documentId, source, confidenceFilter],
    queryFn: async () => {
      const params = new URLSearchParams({
        source,
        minConfidence: confidenceFilter.toString(),
      });

      const response = await fetch(`/api/documents/${documentId}/highlights?${params}`);
      
      if (!response.ok) {
        throw new Error('Failed to fetch highlights');
      }

      return response.json();
    },
    enabled: enabled && !!documentId,
  });

  // Filter highlights by layout type
  const filteredHighlights = highlightsData?.highlights.filter(highlight => {
    if (layoutTypeFilter && highlight.layoutType !== layoutTypeFilter) {
      return false;
    }
    return true;
  }) || [];

  // Handle highlight interactions
  const handleHighlightClick = useCallback((highlight: OCRHighlight) => {
    setSelectedHighlight(highlight);
    // Could emit custom event or call callback if provided
  }, []);

  const handleHighlightHover = useCallback((highlight: OCRHighlight | null) => {
    setHoveredHighlight(highlight);
  }, []);

  // Utility functions
  const clearSelection = useCallback(() => {
    setSelectedHighlight(null);
  }, []);

  const updateConfidenceFilter = useCallback((confidence: number) => {
    setConfidenceFilter(confidence);
  }, []);

  const updateLayoutTypeFilter = useCallback((layoutType: string | null) => {
    setLayoutTypeFilter(layoutType);
  }, []);

  const getHighlightStatistics = useCallback(() => {
    if (!highlightsData?.highlights) return null;

    const highlights = highlightsData.highlights;
    const total = highlights.length;
    
    const confidenceGroups = {
      high: highlights.filter(h => h.confidence >= 0.9).length,
      medium: highlights.filter(h => h.confidence >= 0.7 && h.confidence < 0.9).length,
      low: highlights.filter(h => h.confidence < 0.7).length,
    };

    const layoutTypes = Array.from(new Set(highlights.map(h => h.layoutType)));
    const layoutTypeDistribution = Object.fromEntries(
      layoutTypes.map(type => [
        type,
        highlights.filter(h => h.layoutType === type).length
      ])
    );

    const avgConfidence = highlights.length > 0
      ? highlights.reduce((sum, h) => sum + h.confidence, 0) / highlights.length
      : 0;

    return {
      total,
      averageConfidence: avgConfidence,
      confidenceDistribution: confidenceGroups,
      layoutTypes,
      layoutTypeDistribution,
      filteredCount: filteredHighlights.length,
    };
  }, [highlightsData, filteredHighlights.length]);

  return {
    // Data
    highlights: filteredHighlights,
    highlightsData,
    selectedHighlight,
    hoveredHighlight,
    statistics: getHighlightStatistics(),
    
    // States
    isLoading,
    error,
    
    // Filters
    confidenceFilter,
    layoutTypeFilter,
    
    // Actions
    handleHighlightClick,
    handleHighlightHover,
    clearSelection,
    updateConfidenceFilter,
    updateLayoutTypeFilter,
    refetch,
  };
}