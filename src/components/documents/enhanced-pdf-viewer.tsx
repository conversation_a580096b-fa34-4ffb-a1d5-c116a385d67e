'use client';

import { Viewer } from '@react-pdf-viewer/core';
import '@react-pdf-viewer/core/lib/styles/index.css';
import { defaultLayoutPlugin } from '@react-pdf-viewer/default-layout';
import { pageNavigationPlugin } from '@react-pdf-viewer/page-navigation';
import '@react-pdf-viewer/core/lib/styles/index.css';
import '@react-pdf-viewer/default-layout/lib/styles/index.css';
import React, { useEffect, useRef, useState, useCallback } from 'react';
import { cn } from '@/lib/utils';
import { createHighlightPlugin } from './pdf-highlight-plugin';

// Cache for storing PDF blobs
const pdfCache = new Map<string, Blob>();
const blobUrlCache = new Map<string, string>();

export interface OCRHighlight {
  id: string;
  pageIndex: number;
  boundingBox: {
    left: number;
    top: number;
    width: number;
    height: number;
  };
  polygon?: Array<{ x: number; y: number }>;
  text: string;
  layoutType: string;
  confidence: number;
  chunkId?: string;
}

export interface EnhancedPDFViewerProps {
  url: string;
  documentId?: string;
  highlights?: OCRHighlight[];
  options?: PDFViewerOptions;
  onHighlightClick?: (highlight: OCRHighlight) => void;
  onHighlightHover?: (highlight: OCRHighlight | null) => void;
  showConfidenceIndicators?: boolean;
  enableInteractions?: boolean;
}

export type PDFViewerOptions = {
  initialPage?: number;
  timestamp?: number;
};

interface HighlightLayerProps {
  highlights: OCRHighlight[];
  pageIndex: number;
  pageWidth: number;
  pageHeight: number;
  scale: number;
  onHighlightClick?: (highlight: OCRHighlight) => void;
  onHighlightHover?: (highlight: OCRHighlight | null) => void;
  showConfidenceIndicators?: boolean;
  enableInteractions?: boolean;
}

const HighlightLayer: React.FC<HighlightLayerProps> = ({
  highlights,
  pageIndex,
  pageWidth,
  pageHeight,
  scale,
  onHighlightClick,
  onHighlightHover,
  showConfidenceIndicators = true,
  enableInteractions = true,
}) => {
  const pageHighlights = highlights.filter(h => h.pageIndex === pageIndex);

  if (pageHighlights.length === 0) {
    return null;
  }

  const getHighlightColor = (confidence: number, layoutType: string) => {
    // Color based on confidence
    if (confidence >= 0.9) return 'rgba(34, 197, 94, 0.2)'; // Green - high confidence
    if (confidence >= 0.7) return 'rgba(234, 179, 8, 0.2)'; // Yellow - medium confidence
    return 'rgba(239, 68, 68, 0.2)'; // Red - low confidence
  };

  const getBorderColor = (confidence: number, layoutType: string) => {
    // Darker border colors
    if (confidence >= 0.9) return '#16a34a'; // Green
    if (confidence >= 0.7) return '#ca8a04'; // Yellow
    return '#dc2626'; // Red
  };

  const getLayoutTypeColor = (layoutType: string) => {
    switch (layoutType) {
      case 'TITLE':
      case 'LAYOUT_TITLE':
        return 'rgba(147, 51, 234, 0.3)'; // Purple for titles
      case 'HEADER':
      case 'LAYOUT_HEADER':
        return 'rgba(59, 130, 246, 0.3)'; // Blue for headers
      case 'PARAGRAPH':
      case 'LAYOUT_TEXT':
        return 'rgba(34, 197, 94, 0.2)'; // Green for paragraphs
      default:
        return 'rgba(156, 163, 175, 0.2)'; // Gray for unknown
    }
  };

  return (
    <div
      className="absolute inset-0 pointer-events-none z-10"
      style={{
        width: pageWidth * scale,
        height: pageHeight * scale,
      }}
    >
      <svg
        className="absolute inset-0 w-full h-full"
        viewBox={`0 0 ${pageWidth * scale} ${pageHeight * scale}`}
        preserveAspectRatio="xMinYMin meet"
      >
        {pageHighlights.map((highlight) => {
          const x = highlight.boundingBox.left * pageWidth * scale;
          const y = highlight.boundingBox.top * pageHeight * scale;
          const width = highlight.boundingBox.width * pageWidth * scale;
          const height = highlight.boundingBox.height * pageHeight * scale;

          const fillColor = showConfidenceIndicators 
            ? getHighlightColor(highlight.confidence, highlight.layoutType)
            : getLayoutTypeColor(highlight.layoutType);
          
          const strokeColor = getBorderColor(highlight.confidence, highlight.layoutType);

          return (
            <g key={highlight.id}>
              {/* Main highlight rectangle */}
              <rect
                x={x}
                y={y}
                width={width}
                height={height}
                fill={fillColor}
                stroke={strokeColor}
                strokeWidth={1}
                className={cn(
                  'transition-all duration-200',
                  enableInteractions && 'cursor-pointer hover:opacity-80'
                )}
                style={{ pointerEvents: enableInteractions ? 'all' : 'none' }}
                onClick={() => enableInteractions && onHighlightClick?.(highlight)}
                onMouseEnter={() => enableInteractions && onHighlightHover?.(highlight)}
                onMouseLeave={() => enableInteractions && onHighlightHover?.(null)}
              />
              
              {/* Confidence indicator */}
              {showConfidenceIndicators && (
                <text
                  x={x + 4}
                  y={y + 12}
                  fontSize={10}
                  fill={strokeColor}
                  className="font-mono text-xs font-semibold"
                  style={{ pointerEvents: 'none' }}
                >
                  {Math.round(highlight.confidence * 100)}%
                </text>
              )}
              
              {/* Layout type indicator */}
              <text
                x={x + width - 4}
                y={y + 12}
                fontSize={8}
                fill={strokeColor}
                className="font-mono text-xs"
                textAnchor="end"
                style={{ pointerEvents: 'none' }}
              >
                {highlight.layoutType}
              </text>
            </g>
          );
        })}
      </svg>
    </div>
  );
};

// Plugin is now imported from separate file

export function EnhancedPDFViewer({
  url,
  documentId,
  highlights = [],
  options,
  onHighlightClick,
  onHighlightHover,
  showConfidenceIndicators = true,
  enableInteractions = true,
}: EnhancedPDFViewerProps) {
  const [pdfUrl, setPdfUrl] = useState<string | null>(null);
  const currentPdfUrl = useRef<string | null>(null);
  const [isDocumentLoaded, setIsDocumentLoaded] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [hoveredHighlight, setHoveredHighlight] = useState<OCRHighlight | null>(null);

  const handleHighlightClick = useCallback((highlight: OCRHighlight) => {
    console.log('Highlight clicked:', highlight);
    onHighlightClick?.(highlight);
  }, [onHighlightClick]);

  const handleHighlightHover = useCallback((highlight: OCRHighlight | null) => {
    setHoveredHighlight(highlight);
    onHighlightHover?.(highlight);
  }, [onHighlightHover]);

  const pageNavigationPluginInstance = useRef(pageNavigationPlugin()).current;
  const defaultLayoutPluginInstance = useRef(defaultLayoutPlugin()).current;
  const highlightPluginInstance = useRef(
    createHighlightPlugin({
      highlights,
      onHighlightClick: handleHighlightClick,
      onHighlightHover: handleHighlightHover,
      showConfidenceIndicators,
      enableInteractions,
    })
  ).current;

  // Load and cache PDF data
  useEffect(() => {
    const loadPDF = async () => {
      console.log('Enhanced PDFViewer: Loading PDF from URL:', url);
      setIsDocumentLoaded(false);
      setError(null);
      setIsLoading(true);

      if (blobUrlCache.has(url)) {
        const cachedUrl = blobUrlCache.get(url)!;
        setPdfUrl(cachedUrl);
        currentPdfUrl.current = cachedUrl;
        setIsLoading(false);
        return;
      }

      try {
        let pdfBlob: Blob;

        if (pdfCache.has(url)) {
          pdfBlob = pdfCache.get(url)!;
        } else {
          const response = await fetch(url, {
            method: 'GET',
            headers: {
              Accept: 'application/pdf',
            },
          });

          if (!response.ok) {
            throw new Error(`Failed to fetch PDF: ${response.status} ${response.statusText}`);
          }

          pdfBlob = await response.blob();

          if (!pdfBlob.type.includes('pdf') && pdfBlob.type !== 'application/octet-stream') {
            console.warn('Response is not a PDF, got:', pdfBlob.type);
          }

          pdfCache.set(url, pdfBlob);
        }

        const blobUrl = URL.createObjectURL(pdfBlob);
        blobUrlCache.set(url, blobUrl);
        setPdfUrl(blobUrl);
        currentPdfUrl.current = blobUrl;
        setIsLoading(false);
      } catch (error) {
        console.error('Error loading PDF:', error);
        setError(error instanceof Error ? error.message : 'Failed to load PDF');
        setIsLoading(false);
      }
    };

    loadPDF();

    return () => {
      const urlToCleanup = currentPdfUrl.current;
      if (urlToCleanup) {
        const usageCount = Array.from(blobUrlCache.values()).filter(
          (v) => v === urlToCleanup
        ).length;
        if (usageCount <= 1) {
          URL.revokeObjectURL(urlToCleanup);
          blobUrlCache.delete(url);
        }
      }
    };
  }, [url]);

  // Handle page navigation after document is loaded
  useEffect(() => {
    if (isDocumentLoaded && options?.initialPage) {
      pageNavigationPluginInstance.jumpToPage(options.initialPage - 1);
    }
  }, [isDocumentLoaded, options?.initialPage, options?.timestamp, pageNavigationPluginInstance]);

  const handleDocumentLoad = () => {
    setIsDocumentLoaded(true);
  };

  if (error) {
    return (
      <div className="flex h-full items-center justify-center bg-gray-50">
        <div className="text-center">
          <div className="mb-2 text-red-600">Failed to load PDF</div>
          <div className="text-sm text-gray-500">{error}</div>
          <div className="mt-2 text-xs text-gray-400">URL: {url}</div>
        </div>
      </div>
    );
  }

  if (isLoading || !pdfUrl) {
    return (
      <div className="flex h-full items-center justify-center bg-gray-50">
        <div className="text-center">
          <div className="mx-auto mb-2 h-8 w-8 animate-spin rounded-full border-b-2 border-blue-600"></div>
          <div className="text-gray-600">Loading PDF...</div>
          {highlights.length > 0 && (
            <div className="text-sm text-gray-500 mt-1">
              {highlights.length} highlights available
            </div>
          )}
        </div>
      </div>
    );
  }

  return (
    <div className="relative h-full overflow-y-auto">
      {/* Highlight info panel */}
      {hoveredHighlight && (
        <div className="absolute top-4 right-4 z-50 bg-white border border-gray-200 rounded-lg shadow-lg p-3 max-w-xs">
          <div className="text-sm font-semibold text-gray-800">
            {hoveredHighlight.layoutType}
          </div>
          <div className="text-xs text-gray-600">
            Confidence: {Math.round(hoveredHighlight.confidence * 100)}%
          </div>
          <div className="text-xs text-gray-500 mt-1 line-clamp-3">
            {hoveredHighlight.text}
          </div>
        </div>
      )}

      {/* PDF Viewer */}
      <div className="h-full">
        <Viewer
          fileUrl={pdfUrl}
          defaultScale={1}
          enableSmoothScroll
          plugins={[
            defaultLayoutPluginInstance,
            pageNavigationPluginInstance,
            highlightPluginInstance,
          ]}
          onDocumentLoad={handleDocumentLoad}
        />
      </div>

      {/* Statistics */}
      {highlights.length > 0 && (
        <div className="absolute bottom-4 left-4 bg-white border border-gray-200 rounded-lg shadow p-2 text-xs text-gray-600">
          {highlights.length} highlights • {documentId && `Doc: ${documentId.slice(0, 8)}`}
        </div>
      )}
    </div>
  );
}