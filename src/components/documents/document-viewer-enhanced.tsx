'use client';

import { useState, useCallback } from 'react';
import { EnhancedPDFViewer, type OCRHighlight } from './enhanced-pdf-viewer';
import { HighlightControls } from './highlight-controls';
import { useDocumentHighlights } from '@/hooks/use-document-highlights';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { 
  Copy,
  MessageSquare,
  X,
  ChevronRight,
  ChevronLeft,
  Settings,
} from 'lucide-react';
import { toast } from 'sonner';

interface DocumentViewerEnhancedProps {
  documentId: string;
  documentUrl: string;
  fileName: string;
  onTextReference?: (text: string, context?: any) => void;
  className?: string;
}

export function DocumentViewerEnhanced({
  documentId,
  documentUrl,
  fileName,
  onTextReference,
  className,
}: DocumentViewerEnhancedProps) {
  const [showHighlights, setShowHighlights] = useState(true);
  const [showControls, setShowControls] = useState(true);
  const [showConfidenceIndicators, setShowConfidenceIndicators] = useState(true);
  const [enableInteractions, setEnableInteractions] = useState(true);
  const [selectedHighlight, setSelectedHighlight] = useState<OCRHighlight | null>(null);

  const { highlights, statistics } = useDocumentHighlights(documentId, {
    enabled: showHighlights,
  });

  const handleHighlightClick = useCallback((highlight: OCRHighlight) => {
    setSelectedHighlight(highlight);
    
    // Auto-scroll to highlight if needed
    console.log('Clicked highlight:', {
      text: highlight.text.substring(0, 100) + '...',
      confidence: highlight.confidence,
      layoutType: highlight.layoutType,
    });
  }, []);

  const handleHighlightHover = useCallback((highlight: OCRHighlight | null) => {
    // Could show preview tooltip here
  }, []);

  const copyHighlightText = useCallback(() => {
    if (selectedHighlight) {
      navigator.clipboard.writeText(selectedHighlight.text);
      toast.success('Text copied to clipboard');
    }
  }, [selectedHighlight]);

  const referenceInChat = useCallback(() => {
    if (selectedHighlight && onTextReference) {
      onTextReference(selectedHighlight.text, {
        documentId,
        fileName,
        layoutType: selectedHighlight.layoutType,
        confidence: selectedHighlight.confidence,
      });
      toast.success('Text referenced in chat');
    }
  }, [selectedHighlight, onTextReference, documentId, fileName]);

  const clearSelection = useCallback(() => {
    setSelectedHighlight(null);
  }, []);

  return (
    <div className={`flex h-full bg-gray-50 ${className}`}>
      {/* Controls Panel */}
      {showControls && (
        <div className="w-80 border-r border-gray-200 bg-white p-4 overflow-y-auto">
          <HighlightControls
            documentId={documentId}
            onHighlightToggle={setShowHighlights}
            showConfidenceIndicators={showConfidenceIndicators}
            onShowConfidenceToggle={setShowConfidenceIndicators}
            enableInteractions={enableInteractions}
            onEnableInteractionsToggle={setEnableInteractions}
          />
        </div>
      )}

      {/* PDF Viewer */}
      <div className="flex-1 relative">
        {/* Controls Toggle */}
        <Button
          variant="outline"
          size="sm"
          onClick={() => setShowControls(!showControls)}
          className="absolute top-4 left-4 z-40 bg-white/90 backdrop-blur-sm"
        >
          {showControls ? (
            <>
              <ChevronLeft className="w-4 h-4 mr-1" />
              Hide Controls
            </>
          ) : (
            <>
              <ChevronRight className="w-4 h-4 mr-1" />
              <Settings className="w-4 h-4" />
            </>
          )}
        </Button>

        {/* Document Info */}
        {statistics && (
          <div className="absolute top-4 right-4 z-40 bg-white/90 backdrop-blur-sm border border-gray-200 rounded-lg px-3 py-2">
            <div className="text-xs text-gray-600">{fileName}</div>
            <div className="text-xs text-gray-500">
              {statistics.total} highlights • {Math.round(statistics.averageConfidence * 100)}% avg confidence
            </div>
          </div>
        )}

        {/* Enhanced PDF Viewer */}
        <EnhancedPDFViewer
          url={documentUrl}
          documentId={documentId}
          highlights={showHighlights ? highlights : []}
          onHighlightClick={handleHighlightClick}
          onHighlightHover={handleHighlightHover}
          showConfidenceIndicators={showConfidenceIndicators}
          enableInteractions={enableInteractions}
        />

        {/* Selected Highlight Panel */}
        {selectedHighlight && (
          <Card className="absolute bottom-4 left-4 right-4 z-50 max-w-2xl mx-auto shadow-lg">
            <CardContent className="p-4">
              <div className="flex items-start justify-between gap-4">
                <div className="flex-1 space-y-2">
                  {/* Header */}
                  <div className="flex items-center gap-2">
                    <Badge 
                      variant={selectedHighlight.confidence >= 0.9 ? 'default' : selectedHighlight.confidence >= 0.7 ? 'secondary' : 'destructive'}
                      className="text-xs"
                    >
                      {selectedHighlight.layoutType}
                    </Badge>
                    <Badge variant="outline" className="text-xs">
                      {Math.round(selectedHighlight.confidence * 100)}% confidence
                    </Badge>
                  </div>

                  {/* Content */}
                  <div className="text-sm text-gray-800 leading-relaxed max-h-32 overflow-y-auto">
                    {selectedHighlight.text}
                  </div>

                  {/* Actions */}
                  <div className="flex gap-2 pt-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={copyHighlightText}
                      className="text-xs"
                    >
                      <Copy className="w-3 h-3 mr-1" />
                      Copy
                    </Button>
                    
                    {onTextReference && (
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={referenceInChat}
                        className="text-xs"
                      >
                        <MessageSquare className="w-3 h-3 mr-1" />
                        Reference in Chat
                      </Button>
                    )}
                  </div>
                </div>

                {/* Close button */}
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={clearSelection}
                  className="p-1 h-auto"
                >
                  <X className="w-4 h-4" />
                </Button>
              </div>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  );
}