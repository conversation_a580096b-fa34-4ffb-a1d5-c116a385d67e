'use client';

import { <PERSON><PERSON> } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Slider } from '@/components/ui/slider';
import { Switch } from '@/components/ui/switch';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { 
  Eye,
  EyeOff,
  Target,
  BarChart3,
  FileText,
  Settings,
  RefreshCw,
} from 'lucide-react';
import { useDocumentHighlights } from '@/hooks/use-document-highlights';
import { type OCRHighlight } from './enhanced-pdf-viewer';

interface HighlightControlsProps {
  documentId: string;
  onHighlightToggle?: (enabled: boolean) => void;
  onConfidenceFilterChange?: (confidence: number) => void;
  onLayoutTypeFilterChange?: (layoutType: string | null) => void;
  showConfidenceIndicators?: boolean;
  onShowConfidenceToggle?: (show: boolean) => void;
  enableInteractions?: boolean;
  onEnableInteractionsToggle?: (enable: boolean) => void;
}

export function HighlightControls({
  documentId,
  onHighlightToggle,
  onConfidenceFilterChange,
  onLayoutTypeFilterChange,
  showConfidenceIndicators = true,
  onShowConfidenceToggle,
  enableInteractions = true,
  onEnableInteractionsToggle,
}: HighlightControlsProps) {
  const {
    highlights,
    highlightsData,
    statistics,
    isLoading,
    error,
    confidenceFilter,
    layoutTypeFilter,
    updateConfidenceFilter,
    updateLayoutTypeFilter,
    refetch,
  } = useDocumentHighlights(documentId);

  const handleConfidenceChange = (values: number[]) => {
    const confidence = values[0] / 100; // Convert to 0-1 scale
    updateConfidenceFilter(confidence);
    onConfidenceFilterChange?.(confidence);
  };

  const handleLayoutTypeChange = (value: string) => {
    const layoutType = value === 'all' ? null : value;
    updateLayoutTypeFilter(layoutType);
    onLayoutTypeFilterChange?.(layoutType);
  };

  if (error) {
    return (
      <Card className="border-red-200">
        <CardHeader className="pb-3">
          <CardTitle className="text-sm text-red-600 flex items-center gap-2">
            <Settings className="w-4 h-4" />
            Highlight Controls
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-sm text-red-500">
            Failed to load highlights: {error.message}
          </div>
          <Button
            variant="outline"
            size="sm"
            onClick={() => refetch()}
            className="mt-2"
            disabled={isLoading}
          >
            <RefreshCw className="w-3 h-3 mr-1" />
            Retry
          </Button>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="min-w-64">
      <CardHeader className="pb-3">
        <CardTitle className="text-sm flex items-center gap-2">
          <Target className="w-4 h-4" />
          Highlight Controls
          {isLoading && <div className="w-3 h-3 animate-spin rounded-full border border-blue-500 border-t-transparent" />}
        </CardTitle>
      </CardHeader>
      
      <CardContent className="space-y-4">
        {/* Toggle Highlights */}
        <div className="flex items-center justify-between">
          <Label className="text-xs text-gray-600 flex items-center gap-1">
            <Eye className="w-3 h-3" />
            Show Highlights
          </Label>
          <Switch
            checked={true} // This would be controlled by parent
            onCheckedChange={onHighlightToggle}
            disabled={!highlights.length}
          />
        </div>

        {highlights.length > 0 && (
          <>
            <Separator />

            {/* Statistics */}
            {statistics && (
              <div className="space-y-2">
                <Label className="text-xs text-gray-600 flex items-center gap-1">
                  <BarChart3 className="w-3 h-3" />
                  Statistics
                </Label>
                
                <div className="grid grid-cols-2 gap-2 text-xs">
                  <div>
                    <div className="text-gray-500">Total</div>
                    <div className="font-medium">{statistics.total}</div>
                  </div>
                  <div>
                    <div className="text-gray-500">Filtered</div>
                    <div className="font-medium">{statistics.filteredCount}</div>
                  </div>
                  <div>
                    <div className="text-gray-500">Avg Confidence</div>
                    <div className="font-medium">{Math.round(statistics.averageConfidence * 100)}%</div>
                  </div>
                  <div>
                    <div className="text-gray-500">Layout Types</div>
                    <div className="font-medium">{statistics.layoutTypes.length}</div>
                  </div>
                </div>

                {/* Confidence Distribution */}
                <div className="space-y-1">
                  <div className="text-xs text-gray-500">Confidence Distribution</div>
                  <div className="flex gap-1">
                    <Badge variant="secondary" className="text-xs px-1 py-0 bg-green-100 text-green-700">
                      High: {statistics.confidenceDistribution.high}
                    </Badge>
                    <Badge variant="secondary" className="text-xs px-1 py-0 bg-yellow-100 text-yellow-700">
                      Med: {statistics.confidenceDistribution.medium}
                    </Badge>
                    <Badge variant="secondary" className="text-xs px-1 py-0 bg-red-100 text-red-700">
                      Low: {statistics.confidenceDistribution.low}
                    </Badge>
                  </div>
                </div>
              </div>
            )}

            <Separator />

            {/* Confidence Filter */}
            <div className="space-y-2">
              <Label className="text-xs text-gray-600">
                Min Confidence: {Math.round(confidenceFilter * 100)}%
              </Label>
              <Slider
                value={[confidenceFilter * 100]}
                onValueChange={handleConfidenceChange}
                max={100}
                min={0}
                step={5}
                className="w-full"
              />
            </div>

            {/* Layout Type Filter */}
            {statistics && statistics.layoutTypes.length > 1 && (
              <div className="space-y-2">
                <Label className="text-xs text-gray-600 flex items-center gap-1">
                  <FileText className="w-3 h-3" />
                  Layout Type
                </Label>
                <Select value={layoutTypeFilter || 'all'} onValueChange={handleLayoutTypeChange}>
                  <SelectTrigger className="h-8 text-xs">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Types</SelectItem>
                    {statistics.layoutTypes.map((type) => (
                      <SelectItem key={type} value={type}>
                        {type} ({statistics.layoutTypeDistribution[type]})
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            )}

            <Separator />

            {/* Display Options */}
            <div className="space-y-3">
              <Label className="text-xs text-gray-600">Display Options</Label>
              
              <div className="flex items-center justify-between">
                <Label className="text-xs flex items-center gap-1">
                  <Target className="w-3 h-3" />
                  Confidence Indicators
                </Label>
                <Switch
                  checked={showConfidenceIndicators}
                  onCheckedChange={onShowConfidenceToggle}
                />
              </div>

              <div className="flex items-center justify-between">
                <Label className="text-xs flex items-center gap-1">
                  <Settings className="w-3 h-3" />
                  Enable Interactions
                </Label>
                <Switch
                  checked={enableInteractions}
                  onCheckedChange={onEnableInteractionsToggle}
                />
              </div>
            </div>

            {/* Source Info */}
            {highlightsData && (
              <div className="text-xs text-gray-500 pt-2 border-t">
                Source: {highlightsData.source}
                {highlightsData.processingTime && (
                  <div>Processing: {highlightsData.processingTime}ms</div>
                )}
              </div>
            )}
          </>
        )}

        {highlights.length === 0 && !isLoading && (
          <div className="text-xs text-gray-500 text-center py-4">
            No highlights available for this document
          </div>
        )}
      </CardContent>
    </Card>
  );
}