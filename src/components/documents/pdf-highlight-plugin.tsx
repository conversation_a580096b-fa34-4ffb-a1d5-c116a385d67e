import { Plugin, PluginFunctions } from '@react-pdf-viewer/core';
import React from 'react';
import { type OCRHighlight } from './enhanced-pdf-viewer';

interface HighlightPluginProps {
  highlights: OCRHighlight[];
  onHighlightClick?: (highlight: <PERSON><PERSON><PERSON>ighlight) => void;
  onHighlightHover?: (highlight: OCRHighlight | null) => void;
  showConfidenceIndicators?: boolean;
  enableInteractions?: boolean;
}

interface HighlightOverlayProps {
  highlights: OCRHighlight[];
  pageIndex: number;
  onHighlightClick?: (highlight: OCRHighlight) => void;
  onHighlightHover?: (highlight: OCRHighlight | null) => void;
  showConfidenceIndicators?: boolean;
  enableInteractions?: boolean;
}

const HighlightOverlay: React.FC<HighlightOverlayProps> = ({
  highlights,
  pageIndex,
  onHighlightClick,
  onHighlightHover,
  showConfidenceIndicators = true,
  enableInteractions = true,
}) => {
  const pageHighlights = highlights.filter(h => h.pageIndex === pageIndex);

  if (pageHighlights.length === 0) {
    return null;
  }

  const getHighlightStyle = (highlight: OCRHighlight) => {
    const { boundingBox, confidence, layoutType } = highlight;
    
    // Convert normalized coordinates to percentages
    const left = `${boundingBox.left * 100}%`;
    const top = `${boundingBox.top * 100}%`;
    const width = `${boundingBox.width * 100}%`;
    const height = `${boundingBox.height * 100}%`;

    // Color based on confidence
    let backgroundColor: string;
    let borderColor: string;
    
    if (confidence >= 0.9) {
      backgroundColor = 'rgba(34, 197, 94, 0.2)'; // Green
      borderColor = '#16a34a';
    } else if (confidence >= 0.7) {
      backgroundColor = 'rgba(234, 179, 8, 0.2)'; // Yellow
      borderColor = '#ca8a04';
    } else {
      backgroundColor = 'rgba(239, 68, 68, 0.2)'; // Red
      borderColor = '#dc2626';
    }

    // Layout type specific adjustments
    if (layoutType === 'TITLE' || layoutType === 'LAYOUT_TITLE') {
      backgroundColor = 'rgba(147, 51, 234, 0.3)'; // Purple for titles
      borderColor = '#7c3aed';
    }

    return {
      position: 'absolute' as const,
      left,
      top,
      width,
      height,
      backgroundColor,
      border: `1px solid ${borderColor}`,
      borderRadius: '2px',
      cursor: enableInteractions ? 'pointer' : 'default',
      pointerEvents: (enableInteractions ? 'auto' : 'none') as any,
      transition: 'all 0.2s ease',
      zIndex: 10,
    };
  };

  return (
    <div className="absolute inset-0 pointer-events-none">
      {pageHighlights.map((highlight) => (
        <div
          key={highlight.id}
          style={getHighlightStyle(highlight)}
          className="hover:opacity-80"
          onClick={() => enableInteractions && onHighlightClick?.(highlight)}
          onMouseEnter={() => enableInteractions && onHighlightHover?.(highlight)}
          onMouseLeave={() => enableInteractions && onHighlightHover?.(null)}
        >
          {/* Confidence indicator */}
          {showConfidenceIndicators && (
            <div
              style={{
                position: 'absolute',
                top: '-2px',
                left: '2px',
                fontSize: '8px',
                fontWeight: 'bold',
                color: '#374151',
                backgroundColor: 'rgba(255, 255, 255, 0.9)',
                padding: '1px 3px',
                borderRadius: '2px',
                pointerEvents: 'none',
              }}
            >
              {Math.round(highlight.confidence * 100)}%
            </div>
          )}
          
          {/* Layout type indicator */}
          <div
            style={{
              position: 'absolute',
              top: '-2px',
              right: '2px',
              fontSize: '7px',
              color: '#6b7280',
              backgroundColor: 'rgba(255, 255, 255, 0.8)',
              padding: '1px 2px',
              borderRadius: '1px',
              pointerEvents: 'none',
            }}
          >
            {highlight.layoutType}
          </div>
        </div>
      ))}
    </div>
  );
};

export const createHighlightPlugin = ({
  highlights,
  onHighlightClick,
  onHighlightHover,
  showConfidenceIndicators = true,
  enableInteractions = true,
}: HighlightPluginProps): Plugin => {
  return {
    install: () => {
      // Simplified plugin - in a full implementation, this would integrate
      // with the PDF viewer's rendering system using proper event hooks
      console.log('Highlight plugin installed with', highlights.length, 'highlights');
    },
  };
};