import { db } from '@/database/drizzle';
import { documentChunks, documents, documentSpaces, ocrAnalysis } from '@/database/schema';
import { deleteFromR2 } from '@/lib/cloudflare/r2';
import { type TextractResult } from '@/lib/aws/textract';
import { DocumentType } from '@/types/material';
import { desc, eq } from 'drizzle-orm';

export const MaterialRepository = {
  getMaterialById,
  getMaterials,
  getMaterialsByUserId,
  getMaterialByKey,
  getMaterialWithChunksById,
  storeMaterial,
  storeChunks,
  storeChunksEnhanced,
  storeOcrAnalysis,
  getOcrAnalysis,
  updateMaterialStatus,
  updateMaterialFilename,
  deleteMaterial,
};

export async function getMaterialById(id: string) {
  return db.select().from(documents).where(eq(documents.id, id)).limit(1);
}

export async function getMaterials() {
  return db.select().from(documents).orderBy(desc(documents.createdAt));
}

export async function getMaterialsByUserId(userId: string) {
  return db
    .select()
    .from(documents)
    .where(eq(documents.uploadedBy, userId))
    .orderBy(desc(documents.createdAt));
}

export async function getMaterialByKey(key: string) {
  const material = await db.select().from(documents).where(eq(documents.s3Key, key)).limit(1);
  return material[0];
}

export async function getMaterialWithChunksById(id: string) {
  try {
    const result = await db
      .select({
        id: documents.id,
        fileName: documents.fileName,
        fileType: documents.fileType,
        s3Key: documents.s3Key,
        uploadedBy: documents.uploadedBy,
        createdAt: documents.createdAt,
        summary: documents.summary,
        chunks: {
          id: documentChunks.id,
          content: documentChunks.content,
          embedding: documentChunks.embedding,
          chunkOrder: documentChunks.chunkOrder,
          metadata: documentChunks.metadata,
        },
      })
      .from(documents)
      .leftJoin(documentChunks, eq(documents.id, documentChunks.documentId))
      .where(eq(documents.id, id));

    if (result.length === 0) return null;

    // Transform the flat results into nested structure
    const document = {
      ...result[0],
      chunks: result
        .filter(
          (row): row is typeof row & { chunks: NonNullable<(typeof row)['chunks']> } =>
            row.chunks !== null && row.chunks.id !== null
        )
        .map((row) => row.chunks),
    };

    return document;
  } catch {
    // console.error('Error getting material with chunks by id:', error);
    return null;
  }
}

type SaveMaterialParams = {
  fileName: string;
  fileType: DocumentType;
  s3Key: string;
  uploadedBy: string;
  chunks?: { content: string; pageNumber?: number; chunkOrder: number }[];
  embeddings?: number[][];
  summary?: string | null;
  summaryEmbedding?: number[] | null;
};

export async function storeMaterial({
  fileName,
  fileType,
  s3Key,
  uploadedBy,
  chunks,
  embeddings,
  summary,
  summaryEmbedding,
}: SaveMaterialParams) {
  // Insert the document with summary data
  const [document] = await db
    .insert(documents)
    .values({
      fileName,
      fileType,
      s3Key,
      uploadedBy,
      summary,
      summaryEmbedding,
    })
    .returning();

  if (chunks && embeddings) {
    // Insert the chunks
    await db.insert(documentChunks).values(
      chunks.map((chunk, index) => ({
        documentId: document.id,
        content: chunk.content,
        embedding: embeddings[index],
        pageNumber: chunk.pageNumber,
        chunkOrder: chunk.chunkOrder,
      }))
    );
  }

  return document;
}

type StoreChunksParams = {
  documentId: string;
  chunks: {
    content: string;
    embedding: number[];
    chunkOrder: number;
    metadata: Record<string, unknown>;
  }[];
};

type StoreChunksEnhancedParams = {
  documentId: string;
  chunks: {
    content: string;
    embedding: number[];
    chunkOrder: number;
    metadata: Record<string, unknown>;
    boundingBox?: any;
    polygonCoordinates?: any;
    layoutType?: string;
    confidenceScore?: number;
  }[];
};

export async function storeChunks({ documentId, chunks }: StoreChunksParams) {
  await db.insert(documentChunks).values(
    chunks.map((chunk) => ({
      documentId,
      content: chunk.content,
      embedding: chunk.embedding,
      chunkOrder: chunk.chunkOrder,
      metadata: chunk.metadata || {},
    }))
  );
}

export async function storeChunksEnhanced({ documentId, chunks }: StoreChunksEnhancedParams) {
  await db.insert(documentChunks).values(
    chunks.map((chunk) => ({
      documentId,
      content: chunk.content,
      embedding: chunk.embedding,
      chunkOrder: chunk.chunkOrder,
      metadata: chunk.metadata || {},
      boundingBox: chunk.boundingBox ? JSON.parse(JSON.stringify(chunk.boundingBox)) : null,
      polygonCoordinates: chunk.polygonCoordinates ? JSON.parse(JSON.stringify(chunk.polygonCoordinates)) : null,
      layoutType: chunk.layoutType as any,
      confidenceScore: chunk.confidenceScore,
    }))
  );
}

export async function storeOcrAnalysis(
  documentId: string,
  ocrResult: TextractResult,
  processingTimeMs: number
) {
  const averageConfidence = ocrResult.extractedText.length > 0
    ? Math.round(ocrResult.extractedText.reduce((sum, item) => sum + item.confidence, 0) / ocrResult.extractedText.length * 100)
    : 0;

  await db.insert(ocrAnalysis).values({
    documentId,
    analysisData: ocrResult as any,
    processingTime: processingTimeMs,
    totalBlocks: ocrResult.extractedText.length,
    averageConfidence,
  });
}

export async function getOcrAnalysis(documentId: string) {
  const result = await db
    .select()
    .from(ocrAnalysis)
    .where(eq(ocrAnalysis.documentId, documentId))
    .limit(1);
  
  return result[0] || null;
}

export async function updateMaterialStatus(documentId: string, status: 'uploaded' | 'processing' | 'completed' | 'failed') {
  await db
    .update(documents)
    .set({ status: status as any })
    .where(eq(documents.id, documentId));
}

export async function updateMaterialFilename(id: string, fileName: string) {
  try {
    const [updatedDocument] = await db
      .update(documents)
      .set({
        fileName,
        updatedAt: new Date(),
      })
      .where(eq(documents.id, id))
      .returning();

    return updatedDocument;
  } catch (error) {
    console.error('Error updating material filename:', error);
    throw error;
  }
}

export async function deleteMaterial(id: string, fileKey?: string) {
  try {
    // Hard delete chunks and document_spaces, and soft delete document in a transaction
    const [deletedDocument] = await db.transaction(async (tx) => {
      // Hard delete all associated chunks
      await tx.delete(documentChunks).where(eq(documentChunks.documentId, id));

      // Hard delete all document_spaces connections
      await tx.delete(documentSpaces).where(eq(documentSpaces.documentId, id));

      // Soft delete the document
      const [document] = await tx
        .update(documents)
        .set({
          deletedAt: new Date(),
        })
        .where(eq(documents.id, id))
        .returning();

      return [document];
    });

    if (fileKey) {
      await deleteFromR2(fileKey);
    }

    return deletedDocument;
  } catch (error) {
    console.error('Error deleting material:', error);
    throw error;
  }
}
