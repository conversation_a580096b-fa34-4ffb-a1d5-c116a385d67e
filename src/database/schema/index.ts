import { AgentStep } from '@/hooks/use-chat-enhanced';
import { ChatConfig, DocumentMetadata } from '@/types/chat';
import { DocumentType } from '@/types/material';
import { relations, sql } from 'drizzle-orm';
import {
  boolean,
  index,
  integer,
  jsonb,
  pgEnum,
  pgTable,
  serial,
  text,
  uuid,
  vector,
} from 'drizzle-orm/pg-core';
import { createdAt, deletedAt, enumToPgEnum, timestamptz, updatedAt } from './_helper';

const EMBEDDING_DIMENSION = 1536;

// Users table
export const users = pgTable(
  'users',
  {
    id: uuid('id').defaultRandom().primaryKey(),
    name: text('name').notNull(),
    email: text('email').notNull().unique(),
    password: text('password').notNull(),
    emailVerified: boolean('email_verified').default(false).notNull(),
    verificationToken: text('verification_token'),
    verificationTokenExpiry: timestamptz('verification_token_expiry'),
    resetToken: text('reset_token'),
    resetTokenExpiry: timestamptz('reset_token_expiry'),
    createdAt: createdAt(),
    updatedAt: updatedAt(),
  },
  (table) => ({
    emailIndex: index('email_index').on(table.email),
  })
);

// Lucia Session table
export const sessions = pgTable('session', {
  id: text('id').primaryKey(),
  userId: uuid('user_id')
    .notNull()
    .references(() => users.id),
  expiresAt: timestamptz('expires_at').notNull(),
});

// Define space role enum
export const spaceRoleEnum = pgEnum('space_role', ['owner', 'editor', 'viewer']);

// Spaces table - update with isShared field
export const spaces = pgTable(
  'spaces',
  {
    id: uuid('id').defaultRandom().primaryKey(),
    name: text('name').notNull(),
    description: text('description'),
    ownerId: uuid('owner_id')
      .references(() => users.id)
      .notNull(),
    isShared: boolean('is_shared').default(false).notNull(), // Add sharing flag
    createdAt: createdAt(),
    updatedAt: updatedAt(),
    deletedAt: deletedAt(),
  },
  (table) => ({
    ownerIdIndex: index('owner_id_index').on(table.ownerId),
  })
);

export type Space = typeof spaces.$inferSelect;

export const documentTypes = pgEnum('document_type', enumToPgEnum(DocumentType));

// Document processing status enum
export const documentStatus = pgEnum('document_status', ['uploaded', 'processing', 'completed', 'failed']);

// Layout type enum for OCR data
export const layoutTypeEnum = pgEnum('layout_type', ['PARAGRAPH', 'TITLE', 'HEADER', 'FOOTER', 'SECTION_HEADER', 'LINE', 'LAYOUT_TEXT', 'UNKNOWN']);

// Documents table
export const documents = pgTable(
  'documents',
  {
    id: uuid('id').defaultRandom().primaryKey(),
    fileName: text('file_name').notNull(),
    fileType: documentTypes('file_type').notNull(),
    s3Key: text('s3_key').notNull().unique(),
    uploadedBy: uuid('uploaded_by')
      .references(() => users.id)
      .notNull(),
    // LangGraph enhancement: Add summary fields for dual-layer retrieval
    summary: text('summary'), // Optional for backward compatibility
    summaryEmbedding: vector('summary_embedding', { dimensions: EMBEDDING_DIMENSION }), // Optional
    // Document processing status
    status: documentStatus('status').default('uploaded').notNull(),
    createdAt: createdAt(),
    updatedAt: updatedAt(),
    deletedAt: deletedAt(),
  },
  (table) => ({
    uploadedByIndex: index('uploaded_by_index').on(table.uploadedBy),
    // Add index for summary embedding vector search
    summaryEmbeddingIndex: index('summary_embedding_index').using(
      'hnsw',
      table.summaryEmbedding.op('vector_cosine_ops')
    ),
    // Index for document status
    statusIndex: index('status_index').on(table.status),
  })
);

export type Document = typeof documents.$inferSelect;

// Document chunks table
export const documentChunks = pgTable(
  'document_chunks',
  {
    id: serial('id').primaryKey(),
    documentId: uuid('document_id')
      .references(() => documents.id)
      .notNull(),
    content: text('content').notNull(),
    embedding: vector('embedding', { dimensions: EMBEDDING_DIMENSION }).notNull(),
    chunkOrder: integer('chunk_order').notNull(),
    metadata: jsonb('metadata'),
    // OCR geometry and layout data
    boundingBox: jsonb('bounding_box'),
    polygonCoordinates: jsonb('polygon_coordinates'),
    layoutType: layoutTypeEnum('layout_type'),
    confidenceScore: integer('confidence_score'), // Store as percentage (0-100)
    createdAt: createdAt(),
  },
  (table) => ({
    contentIndex: index('content_index').using('gin', sql`to_tsvector('simple', ${table.content})`),
    documentIdIndex: index('document_id_index').on(table.documentId),
    embeddingIndex: index('embedding_index').using('hnsw', table.embedding.op('vector_cosine_ops')),
    chunkOrderIndex: index('chunk_order_index').on(table.documentId, table.chunkOrder),
    // OCR-specific indexes
    layoutTypeIndex: index('layout_type_index').on(table.layoutType),
    confidenceScoreIndex: index('confidence_score_index').on(table.confidenceScore),
  })
);

export type DocumentChunk = typeof documentChunks.$inferSelect;

// OCR Analysis table for storing Textract results
export const ocrAnalysis = pgTable(
  'ocr_analysis',
  {
    id: uuid('id').defaultRandom().primaryKey(),
    documentId: uuid('document_id')
      .references(() => documents.id, { onDelete: 'cascade' })
      .notNull(),
    textractJobId: text('textract_job_id'), // For future async job tracking
    analysisData: jsonb('analysis_data').notNull(), // Raw Textract response
    processingTime: integer('processing_time'), // Time in milliseconds
    totalBlocks: integer('total_blocks'),
    averageConfidence: integer('average_confidence'), // Average confidence as percentage
    createdAt: createdAt(),
    updatedAt: updatedAt(),
  },
  (table) => ({
    documentIdIndex: index('ocr_document_id_index').on(table.documentId),
    textractJobIdIndex: index('textract_job_id_index').on(table.textractJobId),
  })
);

export type OcrAnalysis = typeof ocrAnalysis.$inferSelect;

// Document-Space association table
export const documentSpaces = pgTable(
  'document_spaces',
  {
    id: serial('id').primaryKey(),
    documentId: uuid('document_id')
      .references(() => documents.id)
      .notNull(),
    spaceId: uuid('space_id')
      .references(() => spaces.id)
      .notNull(),
    createdAt: createdAt(),
  },
  (table) => ({
    documentSpaceIndex: index('document_space_index').on(table.documentId, table.spaceId),
  })
);

// Topics table
export const topics = pgTable(
  'topics',
  {
    id: uuid('id').defaultRandom().primaryKey(),
    name: text('name').notNull(),
    userId: uuid('user_id')
      .references(() => users.id)
      .notNull(),
    config: jsonb('config').$type<ChatConfig>(),
    createdAt: createdAt(),
    updatedAt: updatedAt(),
  },
  (table) => ({
    userIdIndex: index('user_id_index').on(table.userId),
  })
);
export type Topic = typeof topics.$inferSelect;

// Define the sender type enum
export const senderType = pgEnum('senderType', ['user', 'ai']);

export const interactions = pgTable(
  'interactions',
  {
    id: uuid('id').defaultRandom().primaryKey(),
    topicId: uuid('topic_id')
      .references(() => topics.id)
      .notNull(),
    userId: uuid('user_id')
      .references(() => users.id)
      .notNull(),
    content: text('content').notNull(),
    senderType: senderType('sender_type').notNull(),
    retrievedDocuments: jsonb('retrieved_documents'),
    agentSteps: jsonb('agent_steps').$type<AgentStep[]>(), // Store agent steps as JSON for AI messages
    createdAt: createdAt(),
  },
  (table) => ({
    userTopicIndex: index('user_topic_index').on(table.userId, table.topicId),
    createdAtIndex: index('created_at_index').on(table.createdAt),
  })
);

export type Interaction = typeof interactions.$inferSelect;

export type RetrievedDocumentChunkStore = {
  id: string; // documentChunks.id
  chunkId: number; // documentChunks.id
  similarity?: number; // documentChunks.similarity
  metadata: DocumentMetadata | Record<string, never>; // documentChunks.metadata
};

// Space memberships table (many-to-many between users and spaces with roles)
export const spaceMembers = pgTable(
  'space_members',
  {
    id: serial('id').primaryKey(),
    spaceId: uuid('space_id')
      .references(() => spaces.id)
      .notNull(),
    userId: uuid('user_id')
      .references(() => users.id)
      .notNull(),
    role: spaceRoleEnum('role').notNull().default('viewer'),
    invitationId: integer('invitation_id').references(() => spaceInvitations.id),
    createdAt: createdAt(),
    updatedAt: updatedAt(),
  },
  (table) => ({
    userSpaceIndex: index('user_space_index').on(table.userId, table.spaceId),
  })
);

// Space invitations table
export const spaceInvitations = pgTable(
  'space_invitations',
  {
    id: serial('id').primaryKey(),
    spaceId: uuid('space_id')
      .references(() => spaces.id)
      .notNull(),
    code: text('code').notNull().unique(),
    createdBy: uuid('created_by')
      .references(() => users.id)
      .notNull(),
    useCount: integer('use_count').default(0).notNull(),
    createdAt: createdAt(),
    deletedAt: deletedAt(),
  },
  (table) => ({
    spaceIdIndex: index('space_id_index').on(table.spaceId),
    codeIndex: index('code_index').on(table.code),
  })
);

// Update the user relations to include space members and invitations
export const usersRelations = relations(users, ({ many }) => ({
  ownedSpaces: many(spaces),
  documents: many(documents),
  topics: many(topics),
  interactions: many(interactions),
  sessions: many(sessions),
  spaceMembers: many(spaceMembers),
  spaceInvitations: many(spaceInvitations),
}));

// Update space relations to include members and invitations
export const spacesRelations = relations(spaces, ({ one, many }) => ({
  owner: one(users, {
    fields: [spaces.ownerId],
    references: [users.id],
  }),
  documentSpaces: many(documentSpaces),
  members: many(spaceMembers),
  invitations: many(spaceInvitations),
}));

// Add relations for space members
export const spaceMembersRelations = relations(spaceMembers, ({ one }) => ({
  user: one(users, {
    fields: [spaceMembers.userId],
    references: [users.id],
  }),
  space: one(spaces, {
    fields: [spaceMembers.spaceId],
    references: [spaces.id],
  }),
}));

// Add relations for space invitations
export const spaceInvitationsRelations = relations(spaceInvitations, ({ one }) => ({
  space: one(spaces, {
    fields: [spaceInvitations.spaceId],
    references: [spaces.id],
  }),
  creator: one(users, {
    fields: [spaceInvitations.createdBy],
    references: [users.id],
  }),
}));

export const documentsRelations = relations(documents, ({ one, many }) => ({
  uploader: one(users, {
    fields: [documents.uploadedBy],
    references: [users.id],
  }),
  chunks: many(documentChunks),
  documentSpaces: many(documentSpaces),
  ocrAnalysis: many(ocrAnalysis),
}));

export const documentChunksRelations = relations(documentChunks, ({ one }) => ({
  document: one(documents, {
    fields: [documentChunks.documentId],
    references: [documents.id],
  }),
}));

export const ocrAnalysisRelations = relations(ocrAnalysis, ({ one }) => ({
  document: one(documents, {
    fields: [ocrAnalysis.documentId],
    references: [documents.id],
  }),
}));

export const documentSpacesRelations = relations(documentSpaces, ({ one }) => ({
  document: one(documents, {
    fields: [documentSpaces.documentId],
    references: [documents.id],
  }),
  space: one(spaces, {
    fields: [documentSpaces.spaceId],
    references: [spaces.id],
  }),
}));

export const topicsRelations = relations(topics, ({ one, many }) => ({
  user: one(users, {
    fields: [topics.userId],
    references: [users.id],
  }),
  interactions: many(interactions),
}));

export const interactionsRelations = relations(interactions, ({ one }) => ({
  user: one(users, {
    fields: [interactions.userId],
    references: [users.id],
  }),
  topic: one(topics, {
    fields: [interactions.topicId],
    references: [topics.id],
  }),
}));

export const sessionsRelations = relations(sessions, ({ one }) => ({
  user: one(users, {
    fields: [sessions.userId],
    references: [users.id],
  }),
}));
